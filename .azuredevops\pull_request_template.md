Info from Work-Item
  - [ ] Unit tests required

Checked the following
  - [ ] Unit tests run locally 
  - [ ] Have unit tests been added
  - [ ] Manual tests have been performed

Select one
  - [ ] Is minor
  - [ ] Is major ([BREAKING] is in the comment)
  - [ ] Should not trigger a new release ([IGNORE] is in the comment)

[Code Review Checklist](https://dev.azure.com/codeless/codeless/_wiki/wikis/codeless.wiki/35/Code-Review-Checklist)
  - [ ] 1. Code formatting ok
  - [ ] 2. Coding best practices ok
  - [ ] 3. Code according to architecture
  - [ ] 4. Non function requirements ok
  - [ ] 5. No sensitive information in code
  
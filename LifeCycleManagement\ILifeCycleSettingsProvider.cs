﻿namespace Codeless.Framework.LifeCycleManagement
{
    public interface ILifeCycleSettingsProvider
    {
        /// <summary>
        /// To be enabled only when the helth probes are not implemented inside the component
        /// </summary>
        bool HealthProbesEnabled { get; }
        int HealthProbesPort { get; }
        int FinalizeWorkTimeout { get; }
        int ShutdownTimeout { get; }
        string ComponentName { get; }

    }
}

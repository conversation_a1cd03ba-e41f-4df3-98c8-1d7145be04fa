<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <AssemblyName>Codeless.Framework.LifeCycleManagement</AssemblyName>
    <Authors>Codeless</Authors>
    <Company>Codeless Technology B.V.</Company>
    <Product>Codeless.Framework.Utils</Product>
    <Copyright>Copyright © Codeless Technology B.V. 2003-2024</Copyright>
    <RepositoryUrl>https://codeless.visualstudio.com/codeless/_git/Codeless.Framework.LifeCycleManagement</RepositoryUrl>
    <PackageTags>Codeless Framework LifeCycleManagement</PackageTags>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <RootNamespace>Codeless.Framework.LifeCycleManagement</RootNamespace>
    <Version>3.12.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Codeless.Framework.LockManagement" Version="2.0.0" />
    <PackageReference Include="Codeless.Framework.Logging.Standard" Version="4.16.0" />
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
  </ItemGroup>

</Project>


[![Build%20Status](https://dev.azure.com/codeless/codeless/_apis/build/status/Codeless.Framework.LifeCycleManagement?repoName=Codeless.Framework.LifeCycleManagement&branchName=master)](https://dev.azure.com/codeless/codeless/_build/latest?definitionId=221&repoName=Codeless.Framework.LifeCycleManagement&branchName=master)

# Introduction 
The Life Cycle Manager help to have correct health probes for k8s and will help gracefully shutdown. 
There are 2 generic parameters to the Life Cycle Manager
* TStart: This must be an enum with the steps that the app must take before it is ready to start work. For example connecting to a database. 
* TStop: This must be an enum with the steps that the app must take before it can be terminated (process can be killed), for example stopping workers, writing final logs.
ILifeCycleSettingsProvider must be implemented and passed (or injected) in the constructor.

# Startup Cycle
* Starting: This is the initial state, only after StartupCompleted has be called x times does the state move to Accepting Work (x is the number of enum members in the TStart)
* Accepting Work: This is the normal state when the app is live, work can for example be accepting service calls or handeling queue items
  * Each work that is done called WorkStart / WorkDone (in finally block so the WorkDone always executes)
  * This ensures that the Life Cycle Manager knows how much pending work there is

# Shutdown Cycle
If any part of the app encounters a condition that should lead to the app shutting down, RequestShutdown should be called.
At this point the shutdown cycle starts.
* Finalizing Work (FinalizingStarted event): In this phase no new work should be picked up. New service calls should get errors saying that 
the app is shutting down and workers should unsubscribe. The Life Cycle Manager will now wait for the pending work to reach zero. If pending work can take a very longer time to complete,
then pending work can also be requested to cancel.
* Shutting Down (ShutdownStarted): This state is reached when pending work becomes zero, at this point all workers and background thread shut stop.
* Dead: Only after ShutdownCompleted has been called x times does the state move from Shutting Down to Dead (x is the number of enum members in the TStop)

# Probes
There are 3 probes that will be available on the port you specify in the settings.
* startup: /api/health/startup 
k8s calls this untill it it passes or the amount of retries has been exceded, when it passes it starts call readiness and liveness, if it fails it kills the pod)
* readiness: /api/health/readiness
K8s calls this at a set interval, if this fails k8s will stop sending traffic to this pod.
* liveness: /api/health/liveness
K8s calls this at a set interval, if this fails k8s will kill the pod.
The result of the probes are determined by the life cycle phase of the app. 

# Probes Logic
The logic of the probes are:
Starting
* startup: NotOk
* readiness: NotOk
* liveness: Ok
Accepting Work
* startup: Ok
* readiness: Ok
* liveness: Ok
Finalizing Work
* startup: Ok
* readiness: NotOk
* liveness: Ok
Shutting Down
* startup: Ok
* readiness: NotOk
* liveness: Ok
Dead
* startup: Ok
* readiness: NotOk
* liveness: NotOk

# Breaking Changes
## 3.x (2023-06-01)
Breaking changes because Logging.Standard dependency was updated to 4.0 and this version no longer support Unity.
This means that when updating to this version the solution should also not be using Unity anymore.


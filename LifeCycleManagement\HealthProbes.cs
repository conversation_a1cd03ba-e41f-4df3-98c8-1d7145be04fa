﻿using Codeless.Framework.Logging.Standard;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;

namespace Codeless.Framework.LifeCycleManagement
{
    /// <summary>
    /// Implements health probes for startup, readiness and liveness. 
    /// </summary>
    internal class HealthProbes
    {
        private readonly ILifeCycleManager lifeCycleManager;
        private readonly ILogger logger;

        private bool running = true;

        private readonly int reconnectTimeoutMs = 5000;

        public static string HTTPNewLine => $"{(char)13}{(char)10}";

        private const string startupUrl = "/api/health/startup";
        private const string readinessUrl = "/api/health/readiness";
        private const string livenessUrl = "/api/health/liveness";

        private readonly Response responseOk;
        private readonly Response responseNotOk;
        private readonly Response invalidRequest;
        private readonly Response invalidUrl;
        private readonly Response invalidMethod;

        private Response lastSartupState = null;
        private Response lastReadinessState = null;
        private Response lastLivenessState = null;

        public HealthProbes(ILifeCycleManager lifeCycleManager, ILoggerFactory loggerFactory)
        {
            this.lifeCycleManager = lifeCycleManager ?? throw new ArgumentNullException(nameof(lifeCycleManager));
            loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));

            this.logger = loggerFactory.GetLogger(typeof(HealthProbes));

            this.responseOk = new Response(200, "Health probe OK");
            this.responseNotOk = new Response(500, "Health probe failed");
            this.invalidRequest = new Response(400, "Could not process request");
            this.invalidUrl = new Response(404, "Invalid URL, not a valid health probe endpoint");
            this.invalidMethod = new Response(405, "Invalid Method, only GET is supported");
        }

        public void Start(int port)
        {
            this.running = true;
            while (this.running)
            {
                try
                {
                    TryListen(port);
                }
                catch (Exception ex)
                {
                    logger.WriteError($"SocketException: {ex.Message}", ex);
                    Thread.Sleep(reconnectTimeoutMs);
                }
            }
        }
        public void Stop()
        {
            this.running = false;
        }

        private void TryListen(int port)
        {
            TcpListener server = new TcpListener(IPAddress.Any, port);

            // Start listening for client requests
            server.Start();

            // Buffer for reading data
            byte[] bytes = new byte[1024];

            //Enter the listening loop
            while (this.running)
            {
                // Perform a blocking call to accept requests.
                TcpClient client = server.AcceptTcpClient();

                // Get a stream object for reading and writing
                NetworkStream stream = client.GetStream();
                int byteCount;

                // Loop to receive all the data sent by the client.
                byteCount = stream.Read(bytes, 0, bytes.Length);

                while (byteCount != 0)
                {
                    ProcessOneMessage(bytes, byteCount, stream);
                    byteCount = stream.Read(bytes, 0, bytes.Length);
                }

                client.Close();
            }
        }

        private void ProcessOneMessage(byte[] bytes, int byteCount, NetworkStream stream)
        {
            // Read input
            string data = Encoding.ASCII.GetString(bytes, 0, byteCount);

            // Get response
            Response response = ProcessRequest(data);

            // Send response.
            byte[] msg = Encoding.ASCII.GetBytes(response.HttpResponse);
            stream.Write(msg, 0, msg.Length);
        }

        private Response ProcessRequest(string data)
        {
            // Process the data sent by the client.
            data = data.ToLower();
            data = data.Replace(HealthProbes.HTTPNewLine, "\n");
            string[] lines = data.Split(new char[1] { '\n' }, StringSplitOptions.RemoveEmptyEntries);

            if (lines.Length > 0)

                if (lines[0].StartsWith("get ") &&
                    lines[0].Length > 4)
                {
                    return ProcessGetRequest(lines[0].Substring(4));
                }
                else
                {
                    logger.WriteError(this.invalidMethod.FriendlyResponse);
                    return this.invalidMethod;
                }
            else
            {
                logger.WriteError(this.invalidRequest.FriendlyResponse);
                return this.invalidRequest;
            }
        }

        private Response ProcessGetRequest(string url)
        {
            Response response = null;

            if (url.Contains(startupUrl))
            {
                response = this.lifeCycleManager.InStartup ? this.responseNotOk : this.responseOk;
                WriteProbeChangedMessage("Startup", lastSartupState, response);
                this.lastSartupState = response;
            }
            else if (url.Contains(readinessUrl))
            {
                response = this.lifeCycleManager.AcceptingWork ? this.responseOk : this.responseNotOk;
                WriteProbeChangedMessage("Readiness", lastReadinessState, response);
                this.lastReadinessState = response;
            }
            else if (url.Contains(livenessUrl))
            {
                response = this.lifeCycleManager.IsDead ? this.responseNotOk : this.responseOk;
                WriteProbeChangedMessage("Liveness", lastLivenessState, response);
                this.lastLivenessState = response;
            }

            if (response != null)
            {
                return response;
            }
            else
            {
                string message = $"{this.lifeCycleManager.ComponentName}: {this.invalidUrl.FriendlyResponse}.";

                logger.WriteError(message);
                return this.invalidUrl;
            }
        }

        private void WriteProbeChangedMessage(string probeName, Response oldState, Response newState)
        {
            if (oldState != newState)
            {
                string oldStateText = GetStateText(oldState);
                string newStateText = GetStateText(newState);

                string message = $"{this.lifeCycleManager.ComponentName}: {probeName} health probe changed state from [{oldStateText}] to [{newStateText}].";

                if (!newStateText.Contains("Ok"))
                {
                    this.logger.WriteError(message);
                }
                else if (this.logger.IsInfoEnabled)
                {
                    this.logger.WriteInfo(message);
                }
            }
        }

        private static string GetStateText(Response response)
        {
            if (response != null)
            {
                return response.FriendlyResponse;
            }
            else
            {
                return "Unknown";
            }
        }

        private class Response
        {
            private readonly Dictionary<int, string> codeTextLookup = new Dictionary<int, string>()
            {
                { 200, "OK" },
                { 400, "Bad Request" },
                { 404, "Not Found" },
                { 405, "Method Not Allowed" },
                { 500, "Internal Server Error" }
            };

            public string HttpResponse { get; }

            public string FriendlyResponse { get; }


            public Response(int code, string content)
            {
                string response = GetResponseTemplate();

                if (codeTextLookup.TryGetValue(code, out string codeText))
                {
                    response = response.Replace("[Code]", code.ToString());
                    response = response.Replace("[CodeText]", codeText);
                    response = response.Replace("[Size]", (content?.Length ?? 0).ToString());
                    response = response.Replace("[Content]", content);

                    this.HttpResponse = response;
                    this.FriendlyResponse = content;
                }
                else
                {
                    throw new ArgumentException("Non supported http return code.");
                }
            }

            private static string GetResponseTemplate()
            {
                List<string> responseTemplateList = new List<string>()
                {
                    "HTTP/1.1 [Code] [CodeText]",
                    "Content-Length: [Size]",
                    "Content-Type: text/plain",
                    "",
                    "[Content]"
                };

                return string.Join(HTTPNewLine, responseTemplateList.ToArray());
            }
        }
    }
}

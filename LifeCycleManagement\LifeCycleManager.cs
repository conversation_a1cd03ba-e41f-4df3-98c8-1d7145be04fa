﻿using Codeless.Framework.LockManagement;
using Codeless.Framework.Logging.Standard;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Codeless.Framework.LifeCycleManagement
{
    public class LifeCycleManager<TStart, TStop> : ILifeCycleManager<TStart, TStop> where TStart : Enum where TStop : Enum
    {
        private readonly int startupStepsCount;
        private readonly int shutdownStepsCount;

        private readonly HashSet<TStart> startupStepsCompleted = new HashSet<TStart>();
        private readonly HashSet<TStop> shutdownStepsCompleted = new HashSet<TStop>();

        /// <summary>
        /// This value is only used for moment between that no new work is accepted and checking how much work is still in progress.
        /// In theory, there could be threads that have already passed the check of if work is still allowed, but have no incremented the work counter yet.
        /// Two points in code are marked with "waitForWorkStartCallsMs-Start" and "waitForWorkStartCallsMs-End".
        /// This value must be high enough to ensure that there are not more threads between these two points in code.
        /// </summary>
        private const int waitForWorkStartCallsMs = 100;

        private readonly ILogger logger;
        private readonly ILockManager lockManager;

        /// <summary>
        /// This is needed here to ensure it is not garbage collected
        /// </summary>
#pragma warning disable S1450 // Private fields only used as local variables in methods should become local variables
        private readonly HealthProbes healthProbes;
#pragma warning restore S1450 // Private fields only used as local variables in methods should become local variables

        private LifeCyclePhase lifeCyclePhase = LifeCyclePhase.Starting;
        private long pendingWork = 0;

        public bool AcceptingWork => lifeCyclePhase == LifeCyclePhase.AcceptingWork;
        public bool ShuttingDownWork => lifeCyclePhase == LifeCyclePhase.FinalizingWork || lifeCyclePhase == LifeCyclePhase.ShuttingDown || lifeCyclePhase == LifeCyclePhase.Dead;

        public bool InStartup => lifeCyclePhase == LifeCyclePhase.Starting;

        public bool IsDead => lifeCyclePhase == LifeCyclePhase.Dead;

        string ILifeCycleManager.ComponentName => componentName;

        private readonly string componentName;

        public event EventHandler ShutdownStarted;
        internal void OnShutdownStarted()
        {
            ShutdownStarted?.Invoke(this, new EventArgs());
        }

        public event EventHandler FinalizingStarted;
        internal void OnFinalizingStarted()
        {
            FinalizingStarted?.Invoke(this, new EventArgs());
        }

        private const int defaultShutdownTimeout = 60;
        private const int defaultFinalizeWorkTimeout = 60;

        private readonly int shutdownTimeout;
        private readonly int finalizeWorkTimeout;

        private System.Timers.Timer workFinalizeTimer;
        private System.Timers.Timer shutdownTimer;

        public LifeCycleManager(ILifeCycleSettingsProvider lifeCycleSettingsProvider, ILoggerFactory loggerFactory, ILockManagerFactory lockManagerFactory)
        {
            lifeCycleSettingsProvider = lifeCycleSettingsProvider ?? throw new ArgumentNullException(nameof(lifeCycleSettingsProvider));
            loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            lockManagerFactory = lockManagerFactory ?? throw new ArgumentNullException(nameof(lockManagerFactory));
            this.logger = loggerFactory.GetLogger(typeof(LifeCycleManager<TStart, TStop>));
            this.lockManager = lockManagerFactory.GetLockManager(typeof(LifeCycleManager<TStart, TStop>));
            this.startupStepsCount = Enum.GetValues(typeof(TStart)).Length;
            this.shutdownStepsCount = Enum.GetValues(typeof(TStop)).Length;

            if (lifeCycleSettingsProvider.HealthProbesEnabled)
            {
                this.healthProbes = new HealthProbes(this, loggerFactory);
                Task.Run(() => this.healthProbes.Start(lifeCycleSettingsProvider.HealthProbesPort));                
            }

            componentName = lifeCycleSettingsProvider.ComponentName;
            shutdownTimeout = lifeCycleSettingsProvider.ShutdownTimeout;
            finalizeWorkTimeout = lifeCycleSettingsProvider.FinalizeWorkTimeout;

            // Default to timeout values if not provided.
            if (shutdownTimeout == 0) shutdownTimeout = defaultShutdownTimeout;
            if (finalizeWorkTimeout == 0) finalizeWorkTimeout = defaultFinalizeWorkTimeout;
        }

        public void WorkStart()
        {
            // See comment about waitForWorkStartCallsMs, this it the point "waitForWorkStartCallsMs-Start"
            switch (lifeCyclePhase)
            {
                case LifeCyclePhase.AcceptingWork:
                case LifeCyclePhase.Starting:
                    Interlocked.Increment(ref this.pendingWork);
                    // See comment about waitForWorkStartCallsMs, this it the point "waitForWorkStartCallsMs-End"
                    break;

                case LifeCyclePhase.FinalizingWork:
                case LifeCyclePhase.ShuttingDown:
                case LifeCyclePhase.Dead:
                    throw new InvalidOperationException("Service is shutting down, no calls allowed anymore");

                default:
                    throw new ArgumentException("Invalid lifeCyclePhase");
            }
        }

        public void WorkDone()
        {
            Interlocked.Decrement(ref this.pendingWork);

            if (this.pendingWork == 0)
            {
                lockManager.LockWriteMode(() =>
                {
                    CheckIfWorkIsFinalized();
                });
            }
        }

        public void StartupCompleted(TStart startupStep)
        {
            lockManager.LockWriteMode(() =>
            {
                if (!startupStepsCompleted.Contains(startupStep))
                {
                    if (logger.IsInfoEnabled)
                    {
                        logger.WriteInfo($"Startup step {startupStep} completed");
                    }

                    startupStepsCompleted.Add(startupStep);
                }
                else
                {
                    logger.WriteInfo($"Startup step {startupStep} has already been reported as started, only expecting this to be called once");
                }

                if (logger.IsInfoEnabled)
                {
                    logger.WriteInfo($"Startup steps now {startupStepsCompleted.Count}/{this.startupStepsCount} completed");
                }

                if (startupStepsCompleted.Count == this.startupStepsCount && lifeCyclePhase == LifeCyclePhase.Starting)
                {
                    lifeCyclePhase = LifeCyclePhase.AcceptingWork;

                    if (logger.IsInfoEnabled)
                    {
                        logger.WriteInfo($"Startup completed: now accepting work.");
                    }
                }
            });
        }

        public void RequestShutdown()
        {
            lockManager.LockWriteMode(() =>
            {
                if (logger.IsInfoEnabled)
                {
                    logger.WriteInfo("Shutdown requested");
                }

                if (lifeCyclePhase == LifeCyclePhase.Starting ||
                    lifeCyclePhase == LifeCyclePhase.AcceptingWork)
                {
                    lifeCyclePhase = LifeCyclePhase.FinalizingWork;
                    OnFinalizingStarted();
                    CheckIfWorkIsFinalized();

                    if (lifeCyclePhase != LifeCyclePhase.ShuttingDown)
                    {
                        // Wait for x amount of time for work to finalize
                        workFinalizeTimer = new System.Timers.Timer(finalizeWorkTimeout * 1000);
                        workFinalizeTimer.Elapsed += WorkFinallized_Timeout;
                        workFinalizeTimer.AutoReset = false;
                        workFinalizeTimer.Start();
                    }
                }
            });
        }

        private void CheckIfWorkIsFinalized()
        {
            if (lifeCyclePhase == LifeCyclePhase.FinalizingWork)
            {
                if (logger.IsInfoEnabled)
                {
                    logger.WriteInfo("Checking if work is finalized.");
                }

                // Wait for possible parallel WorkStart calls to complete
                Thread.Sleep(waitForWorkStartCallsMs);

                if (this.pendingWork == 0)
                {
                    if (logger.IsInfoEnabled)
                    {
                        logger.WriteInfo("Checking if work is finalized.");
                    }

                    StartShutDown();
                }
            }
        }

        private void StartShutDown()
        {
            // Disable time-out timer
            if (workFinalizeTimer != null)
            {
                workFinalizeTimer.Stop();
                workFinalizeTimer.Elapsed -= WorkFinallized_Timeout;
            }

            lifeCyclePhase = LifeCyclePhase.ShuttingDown;
            OnShutdownStarted();

            // Wait for x amount of time for work to finalize
            shutdownTimer = new System.Timers.Timer(shutdownTimeout * 1000);
            shutdownTimer.Elapsed += Shutdown_Timeout;
            shutdownTimer.AutoReset = false;
            shutdownTimer.Start();
        }

        public void ShutdownCompleted(TStop shutdownStep)
        {
            this.lockManager.LockWriteMode(() =>
            {
                if (!shutdownStepsCompleted.Contains(shutdownStep))
                {
                    if (logger.IsInfoEnabled)
                    {
                        logger.WriteInfo($"Shutdown step {shutdownStep} completed");
                    }

                    shutdownStepsCompleted.Add(shutdownStep);
                }
                else
                {
                    logger.WriteWarning($"Shutdown step {shutdownStep} has already been reported as shutdown, only expecting this to be called once");
                }

                if (logger.IsInfoEnabled)
                {
                    logger.WriteInfo($"Shutdown steps now {shutdownStepsCompleted.Count}/{this.shutdownStepsCount} completed");
                }

                if (shutdownStepsCompleted.Count == this.shutdownStepsCount)
                {
                    if (logger.IsInfoEnabled)
                    {
                        logger.WriteInfo($"Shutdown completed: ready to die.");
                    }

                    ShutdownCompletedAllSteps();
                }
            });
        }

        private void ShutdownCompletedAllSteps()
        {
            // Disable time-out timer
            if (shutdownTimer != null)
            {
                shutdownTimer.Stop();
                shutdownTimer.Elapsed -= Shutdown_Timeout;
            }

            lifeCyclePhase = LifeCyclePhase.Dead;
        }

        private void WorkFinallized_Timeout(object sender, System.Timers.ElapsedEventArgs e)
        {
            lockManager.LockWriteMode(() =>
            {
                if (lifeCyclePhase != LifeCyclePhase.ShuttingDown)
                {
                    logger.WriteWarning($"Timeout: Waited for work to finallize for {finalizeWorkTimeout} seconds, now forcing shutdown to start");

                    StartShutDown();
                }
            });
        }
        private void Shutdown_Timeout(object sender, System.Timers.ElapsedEventArgs e)
        {
            lockManager.LockWriteMode(() =>
            {
                if (lifeCyclePhase != LifeCyclePhase.Dead)
                {
                    logger.WriteWarning($"Timeout: Waited for shutdown for {finalizeWorkTimeout} seconds, now forcing state dead. ");

                    ShutdownCompletedAllSteps();
                }
            });
        }

        public bool IsStartupStepCompleted(TStart startupStep)
        {
            return startupStepsCompleted.Contains(startupStep);
        }

        public bool IsShutdownStepCompleted(TStop shutdownStep)
        {
            return shutdownStepsCompleted.Contains(shutdownStep);
        }
    }
}

﻿using System;

namespace Codeless.Framework.LifeCycleManagement
{
    public interface ILifeCycleManager<in TStart, in TStop> : ILifeCycleManager where TStart : Enum where TStop : Enum
    {
        void StartupCompleted(TStart startupStep);
        void ShutdownCompleted(TStop shutdownStep);
        bool IsStartupStepCompleted(TStart startupStep);
        bool IsShutdownStepCompleted(TStop shutdownStep);
    }

    public interface ILifeCycleManager
    {
        event EventHandler FinalizingStarted;
        event EventHandler ShutdownStarted;
        void WorkStart();
        void WorkDone();

        bool AcceptingWork { get; }
        bool ShuttingDownWork { get; }
        bool InStartup { get; }
        bool IsDead { get; }
        string ComponentName { get; }
        void RequestShutdown();
    }
}

﻿using System;
using System.Runtime.Serialization;

namespace Codeless.Framework.LifeCycleManagement
{
    [Serializable]
    public class HealthProbeException : Exception
    {
        protected HealthProbeException(SerializationInfo serializationInfo, StreamingContext streamingContext) : base(serializationInfo, streamingContext)
        {
        }

        public HealthProbeException() : base()
        {
        }

        public HealthProbeException(string message) : base(message)
        {
        }
    }
}

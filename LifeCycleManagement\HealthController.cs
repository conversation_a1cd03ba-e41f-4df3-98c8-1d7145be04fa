﻿using Codeless.Framework.Logging.Standard;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;

namespace Codeless.Framework.LifeCycleManagement
{
    [Route("api/[controller]")]
    [ApiController]
    public class HealthController : ControllerBase
    {
        private readonly ILifeCycleManager lifeCycleManager;
        private readonly ILogger logger;
        private const string probeOkResult = "Ok";

        private static bool? lastStartupState = null;
        private static bool? lastReadinessState = null;
        private static bool? lastLivenessState = null;

        public HealthController(ILifeCycleManager lifeCycleManager, ILoggerFactory loggerFactory)
        {
            this.lifeCycleManager = lifeCycleManager ?? throw new ArgumentNullException(nameof(lifeCycleManager));
            loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            this.logger = loggerFactory.GetLogger(typeof(HealthController));
        }

        private static void UpdateLastStartupState(bool value)
        {
            lastStartupState = value;
        }

        private static void UpdateLastReadinessState(bool value)
        {
            lastReadinessState = value;
        }

        private static void UpdateLastLivenessState(bool value)
        {
            lastLivenessState = value;
        }

        [EnableCors]
        [HttpGet("startup")]
        public string Startup()
        {
            if (this.lifeCycleManager.InStartup)
            {
                WriteProbeChangedMessage("Startup", lastStartupState, false);
                UpdateLastStartupState(false);
                throw new HealthProbeException("API is still starting, not ready to accept calls.");
            }
            else
            {
                WriteProbeChangedMessage("Startup", lastStartupState, true);
                UpdateLastStartupState(true);
                return probeOkResult;
            }
        }

        [EnableCors]
        [HttpGet("readiness")]
        public string Readiness()
        {
            if (this.lifeCycleManager.AcceptingWork)
            {
                WriteProbeChangedMessage("Readiness", lastReadinessState, true);
                UpdateLastReadinessState(true);
                return probeOkResult;
            }
            else
            {
                WriteProbeChangedMessage("Readiness", lastReadinessState, false);
                UpdateLastReadinessState(false);
                throw new HealthProbeException("API is shutting down, no new calls.");
            }
        }

        [EnableCors]
        [HttpGet("liveness")]
        public string Liveness()
        {
            if (this.lifeCycleManager.IsDead)
            {
                WriteProbeChangedMessage("Liveness", lastLivenessState, false);
                UpdateLastLivenessState(false);
                throw new HealthProbeException("API is dead and should be terminated.");
            }
            else
            {
                WriteProbeChangedMessage("Liveness", lastLivenessState, true);
                UpdateLastLivenessState(true);
                return probeOkResult;
            }
        }

        private void WriteProbeChangedMessage(string probeName, bool? oldState, bool newState)
        {
            if (oldState != newState)
            {
                string oldStateText = GetStateText(oldState);
                string newStateText = GetStateText(newState);

                string message = $"{this.lifeCycleManager.ComponentName}: {probeName} health probe changed state from [{oldStateText}] to [{newStateText}].";

                if (!newState)
                {
                    this.logger.WriteError(message);
                }
                else if (this.logger.IsInfoEnabled)
                {
                    this.logger.WriteInfo(message);
                }
            }
        }

        private static string GetStateText(bool? lastStartupState)
        {
            if (lastStartupState.HasValue)
            {
                if (lastStartupState.Value)
                {
                    return "Passed";
                }
                else
                {
                    return "Failed";
                }
            }
            else
            {
                return "Unknown";
            }
        }
    }
}
